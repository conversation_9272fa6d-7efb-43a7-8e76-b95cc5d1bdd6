// src/services/chatbotService.ts
import type { ApiAnalysisResponse, AIAgentType } from '../types';
import type { AppNodeType } from '../App';
import { geminiService } from './geminiService';

export interface ChatContext {
  currentAnalysisData: ApiAnalysisResponse | null;
  nodes: AppNodeType[];
  selectedScenes: string[];
}

export interface ParsedCommand {
  intent: string;
  action: string;
  parameters: Record<string, any>;
  confidence: number;
  originalText: string;
}

export interface CommandPattern {
  pattern: RegExp;
  intent: string;
  action: string;
  extractParams: (match: RegExpMatchArray, context: ChatContext) => Record<string, any>;
}

// Command patterns for natural language processing
const COMMAND_PATTERNS: CommandPattern[] = [
  // AI Agent Commands
  {
    pattern: /add\s+(subtitle|audio|video|content|color|object|scene|transition|noise)\s*(?:agent|processor|enhancer|analyzer|grader|detector|classifier|suggester|reducer)?\s*(?:to|for|on)\s*(?:scene\s*)?(\d+|all|every|each)/i,
    intent: 'add_ai_agent',
    action: 'add_agent_to_scene',
    extractParams: (match, context) => {
      const agentTypeMap: Record<string, AIAgentType> = {
        'subtitle': 'subtitle-generator',
        'audio': 'audio-processor',
        'video': 'video-enhancer',
        'content': 'content-analyzer',
        'color': 'color-grader',
        'object': 'object-detector',
        'scene': 'scene-classifier',
        'transition': 'transition-suggester',
        'noise': 'noise-reducer'
      };
      
      const agentType = agentTypeMap[match[1].toLowerCase()] || 'content-analyzer';
      const target = match[2].toLowerCase();
      
      let sceneIds: string[] = [];
      if (target === 'all' || target === 'every' || target === 'each') {
        sceneIds = context.currentAnalysisData?.scenes.map(s => s.sceneId) || [];
      } else {
        const sceneIndex = parseInt(target) - 1;
        const scene = context.currentAnalysisData?.scenes[sceneIndex];
        if (scene) sceneIds = [scene.sceneId];
      }
      
      return { agentType, sceneIds, target };
    }
  },
  
  // Scene Manipulation Commands
  {
    pattern: /(?:create|add|make)\s+(?:a\s+)?(?:new\s+)?scene\s*(?:after|before)?\s*(?:scene\s*)?(\d+)?/i,
    intent: 'create_scene',
    action: 'create_new_scene',
    extractParams: (match, context) => {
      const position = match[1] ? parseInt(match[1]) : (context.currentAnalysisData?.scenes.length || 0) + 1;
      return { position };
    }
  },
  
  {
    pattern: /(?:delete|remove)\s+scene\s*(\d+)/i,
    intent: 'delete_scene',
    action: 'delete_scene',
    extractParams: (match) => ({
      sceneIndex: parseInt(match[1]) - 1
    })
  },
  
  // Enhancement Commands
  {
    pattern: /enhance\s+(?:video\s+)?quality\s*(?:for|of)?\s*(?:all\s+scenes|scene\s*(\d+)|everything)?/i,
    intent: 'enhance_quality',
    action: 'enhance_video_quality',
    extractParams: (match, context) => {
      if (match[1]) {
        const sceneIndex = parseInt(match[1]) - 1;
        const scene = context.currentAnalysisData?.scenes[sceneIndex];
        return { sceneIds: scene ? [scene.sceneId] : [] };
      }
      return { sceneIds: context.currentAnalysisData?.scenes.map(s => s.sceneId) || [] };
    }
  },
  
  // Analysis Commands
  {
    pattern: /(?:analyze|show|tell me about)\s+(?:scene\s*(\d+)|all\s+scenes|project|video)/i,
    intent: 'analyze_content',
    action: 'analyze_scenes',
    extractParams: (match, context) => {
      if (match[1]) {
        const sceneIndex = parseInt(match[1]) - 1;
        const scene = context.currentAnalysisData?.scenes[sceneIndex];
        return { sceneIds: scene ? [scene.sceneId] : [], specific: true };
      }
      return { sceneIds: context.currentAnalysisData?.scenes.map(s => s.sceneId) || [], specific: false };
    }
  },
  
  // Statistics Commands
  {
    pattern: /(?:show|get|display)\s+(?:me\s+)?(?:scene\s+)?(?:statistics|stats|info|information)/i,
    intent: 'show_statistics',
    action: 'display_project_stats',
    extractParams: (match, context) => ({
      sceneCount: context.currentAnalysisData?.scenes.length || 0,
      totalDuration: context.currentAnalysisData?.metadata.duration || 0
    })
  },
  
  // Help Commands
  {
    pattern: /(?:help|what can you do|commands|how to)/i,
    intent: 'help',
    action: 'show_help',
    extractParams: () => ({})
  },
  
  // Batch Operations
  {
    pattern: /(?:add\s+subtitles?\s+to\s+all|subtitle\s+all\s+scenes|generate\s+subtitles?\s+for\s+everything)/i,
    intent: 'batch_subtitles',
    action: 'add_subtitles_to_all',
    extractParams: (match, context) => ({
      sceneIds: context.currentAnalysisData?.scenes.map(s => s.sceneId) || []
    })
  }
];

export class ChatbotService {
  async parseCommand(input: string, context: ChatContext): Promise<ParsedCommand> {
    const normalizedInput = input.trim().toLowerCase();
    
    // Try to match against predefined patterns
    for (const pattern of COMMAND_PATTERNS) {
      const match = normalizedInput.match(pattern.pattern);
      if (match) {
        const parameters = pattern.extractParams(match, context);
        return {
          intent: pattern.intent,
          action: pattern.action,
          parameters,
          confidence: 0.9,
          originalText: input
        };
      }
    }
    
    // If no pattern matches, use AI to understand the intent
    try {
      const aiParsedCommand = await this.parseWithAI(input, context);
      return aiParsedCommand;
    } catch (error) {
      console.warn('AI parsing failed, using fallback:', error);
      return this.createFallbackCommand(input);
    }
  }
  
  private async parseWithAI(input: string, context: ChatContext): Promise<ParsedCommand> {
    const contextInfo = this.buildContextInfo(context);
    
    const prompt = `You are a video editing assistant. Parse this user command and return a JSON response with the following structure:
{
  "intent": "string (e.g., 'add_ai_agent', 'enhance_quality', 'analyze_content')",
  "action": "string (specific action to take)",
  "parameters": {object with relevant parameters},
  "confidence": number (0-1)
}

Available intents: add_ai_agent, create_scene, delete_scene, enhance_quality, analyze_content, show_statistics, help, batch_subtitles

Context:
${contextInfo}

User command: "${input}"

Parse this command and respond with valid JSON only.`;

    const response = await geminiService.processWithAI('content-analyzer', { prompt }, 'chatbot', 'chat');
    
    if (response.result && typeof response.result === 'object') {
      return {
        intent: response.result.intent || 'unknown',
        action: response.result.action || 'unknown_action',
        parameters: response.result.parameters || {},
        confidence: response.result.confidence || 0.5,
        originalText: input
      };
    }
    
    throw new Error('Invalid AI response format');
  }
  
  private buildContextInfo(context: ChatContext): string {
    const info: string[] = [];
    
    if (context.currentAnalysisData) {
      info.push(`Video: ${context.currentAnalysisData.fileName}`);
      info.push(`Scenes: ${context.currentAnalysisData.scenes.length}`);
      info.push(`Duration: ${Math.round(context.currentAnalysisData.metadata.duration)}s`);
      
      if (context.currentAnalysisData.scenes.length > 0) {
        info.push(`Scene titles: ${context.currentAnalysisData.scenes.map((s, i) => `${i+1}. ${s.title}`).join(', ')}`);
      }
    } else {
      info.push('No video currently loaded');
    }
    
    const aiAgentNodes = context.nodes.filter(n => n.data.type === 'ai-agent');
    if (aiAgentNodes.length > 0) {
      info.push(`Active AI agents: ${aiAgentNodes.length}`);
    }
    
    return info.join('\n');
  }
  
  private createFallbackCommand(input: string): ParsedCommand {
    // Simple keyword-based fallback
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('help')) {
      return {
        intent: 'help',
        action: 'show_help',
        parameters: {},
        confidence: 0.8,
        originalText: input
      };
    }
    
    if (lowerInput.includes('subtitle')) {
      return {
        intent: 'add_ai_agent',
        action: 'add_agent_to_scene',
        parameters: { agentType: 'subtitle-generator', sceneIds: [] },
        confidence: 0.6,
        originalText: input
      };
    }
    
    if (lowerInput.includes('enhance') || lowerInput.includes('improve')) {
      return {
        intent: 'enhance_quality',
        action: 'enhance_video_quality',
        parameters: { sceneIds: [] },
        confidence: 0.6,
        originalText: input
      };
    }
    
    return {
      intent: 'unknown',
      action: 'unknown_action',
      parameters: {},
      confidence: 0.3,
      originalText: input
    };
  }
  
  // Get contextual suggestions based on current state
  getSuggestions(context: ChatContext): string[] {
    const suggestions: string[] = [];
    
    if (context.currentAnalysisData) {
      const sceneCount = context.currentAnalysisData.scenes.length;
      suggestions.push(
        `Add subtitle agent to scene 1`,
        `Enhance video quality for all ${sceneCount} scenes`,
        `Analyze content in all scenes`,
        `Show me project statistics`
      );
      
      if (sceneCount > 1) {
        suggestions.push(`Create smooth transitions between scenes`);
      }
    } else {
      suggestions.push(
        'Help me get started',
        'What can you do?',
        'Show available commands'
      );
    }
    
    return suggestions.slice(0, 5);
  }
}

// Singleton instance
export const chatbotService = new ChatbotService();
